// 记事本应用类型定义

export interface NoteRecord {
  id: string
  title: string
  content: string
  category: string
  tags: string[]
  fields: Record<string, any> // 自定义字段，如微信小程序配置信息
  createdAt: string
  updatedAt: string
}

export interface Category {
  id: string
  name: string
  color: string
  icon: string
  description?: string
}

export interface SearchFilters {
  keyword?: string
  category?: string
  tags?: string[]
  dateRange?: {
    start: string
    end: string
  }
}

export interface AppSettings {
  theme: 'light' | 'dark'
  defaultCategory: string
  autoSave: boolean
  exportFormat: 'json' | 'txt'
}

// 预定义的分类
export const DEFAULT_CATEGORIES: Category[] = [
  {
    id: 'wechat-miniprogram',
    name: '微信小程序',
    color: '#07c160',
    icon: 'mdi:wechat',
    description: '微信小程序相关配置信息'
  },
  {
    id: 'alipay-miniprogram',
    name: '支付宝小程序',
    color: '#1677ff',
    icon: 'mdi:alipay',
    description: '支付宝小程序相关配置信息'
  },
  {
    id: 'payment-config',
    name: '支付配置',
    color: '#f5222d',
    icon: 'mdi:credit-card',
    description: '支付相关配置信息'
  },
  {
    id: 'api-config',
    name: 'API配置',
    color: '#722ed1',
    icon: 'mdi:api',
    description: 'API接口配置信息'
  },
  {
    id: 'database-config',
    name: '数据库配置',
    color: '#fa8c16',
    icon: 'mdi:database',
    description: '数据库连接配置信息'
  },
  {
    id: 'general',
    name: '通用记录',
    color: '#52c41a',
    icon: 'mdi:note-text',
    description: '通用记录信息'
  }
]

// 微信小程序配置字段模板
export const WECHAT_MINIPROGRAM_FIELDS = [
  { key: 'appId', label: 'APPID', type: 'text', required: true },
  { key: 'appSecret', label: 'AppSecret', type: 'password', required: true },
  { key: 'merchantId', label: '商户号', type: 'text', required: false },
  { key: 'secretKey', label: '秘钥ID', type: 'password', required: false },
  { key: 'publicKeyId', label: '公钥ID', type: 'text', required: false },
  { key: 'certSerialNumber', label: '证书序列号', type: 'text', required: false },
  { key: 'platformCertSerialNumber', label: '平台证书序列号', type: 'text', required: false }
]

// 支付配置字段模板
export const PAYMENT_CONFIG_FIELDS = [
  { key: 'merchantId', label: '商户号', type: 'text', required: true },
  { key: 'secretKey', label: '秘钥', type: 'password', required: true },
  { key: 'publicKey', label: '公钥', type: 'textarea', required: false },
  { key: 'privateKey', label: '私钥', type: 'textarea', required: false },
  { key: 'notifyUrl', label: '回调地址', type: 'url', required: false }
]

export interface FieldTemplate {
  key: string
  label: string
  type: 'text' | 'password' | 'textarea' | 'url' | 'email' | 'number'
  required: boolean
  placeholder?: string
  description?: string
}

export const CATEGORY_FIELD_TEMPLATES: Record<string, FieldTemplate[]> = {
  'wechat-miniprogram': WECHAT_MINIPROGRAM_FIELDS,
  'payment-config': PAYMENT_CONFIG_FIELDS
}
