<template>
  <view class="notebook-page bg-secondary-50 min-h-screen">
    <!-- 搜索栏 -->
    <view class="search-section bg-white p-4 shadow-sm">
      <view class="search-bar flex items-center bg-secondary-100 rounded-lg p-3">
        <text class="i-mdi:magnify text-secondary-500 text-lg mr-2"></text>
        <input
          v-model="searchKeyword"
          placeholder="搜索记录..."
          class="flex-1 bg-transparent text-secondary-800 placeholder-secondary-400"
          @input="handleSearch"
        />
        <text
          v-if="searchKeyword"
          class="i-mdi:close text-secondary-500 text-lg ml-2"
          @tap="clearSearch"
        ></text>
      </view>
      
      <!-- 筛选标签 -->
      <view class="filter-tags flex flex-wrap gap-2 mt-3">
        <view
          v-for="category in categories"
          :key="category.id"
          :class="[
            'filter-tag px-3 py-1 rounded-full text-sm border transition-all',
            selectedCategory === category.id
              ? 'bg-primary-500 text-white border-primary-500'
              : 'bg-white text-secondary-600 border-secondary-200'
          ]"
          @tap="toggleCategory(category.id)"
        >
          <text :class="category.icon" class="mr-1"></text>
          {{ category.name }}
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section p-4">
      <view class="stats-grid grid grid-cols-3 gap-3">
        <view class="stat-card bg-white rounded-lg p-3 text-center shadow-sm">
          <text class="stat-number text-xl font-bold text-primary-600">{{ recordStats.total }}</text>
          <text class="stat-label text-sm text-secondary-500 block">总记录</text>
        </view>
        <view class="stat-card bg-white rounded-lg p-3 text-center shadow-sm">
          <text class="stat-number text-xl font-bold text-success-600">{{ todayRecords }}</text>
          <text class="stat-label text-sm text-secondary-500 block">今日新增</text>
        </view>
        <view class="stat-card bg-white rounded-lg p-3 text-center shadow-sm">
          <text class="stat-number text-xl font-bold text-warning-600">{{ categories.length }}</text>
          <text class="stat-label text-sm text-secondary-500 block">分类数</text>
        </view>
      </view>
    </view>

    <!-- 记录列表 -->
    <view class="records-section px-4 pb-20">
      <LoadingSpinner v-if="loading" text="加载记录中..." />

      <EmptyState
        v-else-if="filteredRecords.length === 0"
        :icon="searchKeyword || selectedCategory ? 'i-mdi:magnify' : 'i-mdi:note-text-outline'"
        :title="searchKeyword || selectedCategory ? '没有找到匹配的记录' : '还没有记录'"
        :description="searchKeyword || selectedCategory ? '尝试调整搜索条件或筛选器' : '点击下方按钮添加第一条记录吧！'"
        :action-text="searchKeyword || selectedCategory ? '清除筛选' : '添加记录'"
        :action-icon="searchKeyword || selectedCategory ? 'i-mdi:filter-remove' : 'i-mdi:plus'"
        :action-handler="searchKeyword || selectedCategory ? clearFilters : addRecord"
      />

      <view v-else class="records-list space-y-3">
        <view
          v-for="record in filteredRecords"
          :key="record.id"
          class="record-card bg-white rounded-lg p-4 shadow-sm border-l-4"
          :style="{ borderLeftColor: getCategoryColor(record.category) }"
          @tap="viewRecord(record.id)"
        >
          <view class="record-header flex items-start justify-between mb-2">
            <view class="record-title-section flex-1">
              <text class="record-title text-lg font-semibold text-secondary-800 line-clamp-1">
                {{ record.title }}
              </text>
              <view class="record-meta flex items-center mt-1">
                <text :class="getCategoryIcon(record.category)" class="text-sm mr-1"></text>
                <text class="category-name text-sm text-secondary-500 mr-3">
                  {{ getCategoryName(record.category) }}
                </text>
                <text class="record-date text-sm text-secondary-400">
                  {{ formatDate(record.updatedAt) }}
                </text>
              </view>
            </view>
            <view class="record-actions">
              <text
                class="i-mdi:dots-vertical text-secondary-400 text-lg"
                @tap.stop="showRecordActions(record)"
              ></text>
            </view>
          </view>

          <text class="record-content text-secondary-600 text-sm line-clamp-2 mb-2">
            {{ record.content }}
          </text>

          <view v-if="record.tags.length > 0" class="record-tags flex flex-wrap gap-1">
            <text
              v-for="tag in record.tags.slice(0, 3)"
              :key="tag"
              class="tag px-2 py-1 bg-secondary-100 text-secondary-600 text-xs rounded"
            >
              #{{ tag }}
            </text>
            <text v-if="record.tags.length > 3" class="text-secondary-400 text-xs">
              +{{ record.tags.length - 3 }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 浮动添加按钮 -->
    <view class="fab-container fixed bottom-20 right-4 z-10">
      <view
        class="fab bg-primary-500 w-14 h-14 rounded-full flex items-center justify-center shadow-lg"
        @tap="addRecord"
      >
        <text class="i-mdi:plus text-white text-2xl"></text>
      </view>
    </view>

    <!-- 操作菜单 -->
    <uni-popup ref="actionPopup" type="bottom">
      <view class="action-menu bg-white rounded-t-lg p-4">
        <view class="menu-title text-lg font-semibold text-secondary-800 mb-4 text-center">
          选择操作
        </view>
        <view class="menu-actions space-y-2">
          <view
            class="action-item flex items-center p-3 rounded-lg bg-secondary-50"
            @tap="editRecord"
          >
            <text class="i-mdi:pencil text-primary-500 text-lg mr-3"></text>
            <text class="text-secondary-800">编辑</text>
          </view>
          <view
            class="action-item flex items-center p-3 rounded-lg bg-secondary-50"
            @tap="duplicateRecord"
          >
            <text class="i-mdi:content-copy text-warning-500 text-lg mr-3"></text>
            <text class="text-secondary-800">复制</text>
          </view>
          <view
            class="action-item flex items-center p-3 rounded-lg bg-secondary-50"
            @tap="deleteRecord"
          >
            <text class="i-mdi:delete text-error-500 text-lg mr-3"></text>
            <text class="text-secondary-800">删除</text>
          </view>
        </view>
        <view class="cancel-btn mt-4">
          <view
            class="cancel-action bg-secondary-200 text-center py-3 rounded-lg"
            @tap="closeActionMenu"
          >
            <text class="text-secondary-600">取消</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useNotebookStore } from '@/stores/notebook'
import type { NoteRecord } from '@/types'
import LoadingSpinner from '@/components/LoadingSpinner.vue'
import EmptyState from '@/components/EmptyState.vue'

const notebookStore = useNotebookStore()

// 响应式数据
const searchKeyword = ref('')
const selectedCategory = ref('')
const currentRecord = ref<NoteRecord | null>(null)
const actionPopup = ref()

// 计算属性
const { records, categories, loading, recordStats } = storeToRefs(notebookStore)

const filteredRecords = computed(() => {
  return notebookStore.searchRecords({
    keyword: searchKeyword.value,
    category: selectedCategory.value || undefined
  })
})

const todayRecords = computed(() => {
  const today = new Date().toDateString()
  return records.value.filter(record => 
    new Date(record.createdAt).toDateString() === today
  ).length
})

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const clearSearch = () => {
  searchKeyword.value = ''
}

const clearFilters = () => {
  searchKeyword.value = ''
  selectedCategory.value = ''
}

const toggleCategory = (categoryId: string) => {
  selectedCategory.value = selectedCategory.value === categoryId ? '' : categoryId
}

const getCategoryColor = (categoryId: string) => {
  const category = notebookStore.getCategoryById(categoryId)
  return category?.color || '#64748b'
}

const getCategoryIcon = (categoryId: string) => {
  const category = notebookStore.getCategoryById(categoryId)
  return category?.icon || 'i-mdi:note-text'
}

const getCategoryName = (categoryId: string) => {
  const category = notebookStore.getCategoryById(categoryId)
  return category?.name || '未知分类'
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

const viewRecord = (recordId: string) => {
  uni.navigateTo({
    url: `/pages/notebook/detail?id=${recordId}`
  })
}

const addRecord = () => {
  uni.navigateTo({
    url: '/pages/notebook/edit'
  })
}

const showRecordActions = (record: NoteRecord) => {
  currentRecord.value = record
  actionPopup.value?.open()
}

const closeActionMenu = () => {
  actionPopup.value?.close()
}

const editRecord = () => {
  if (currentRecord.value) {
    uni.navigateTo({
      url: `/pages/notebook/edit?id=${currentRecord.value.id}`
    })
    closeActionMenu()
  }
}

const duplicateRecord = async () => {
  if (currentRecord.value) {
    try {
      await notebookStore.addRecord({
        title: `${currentRecord.value.title} (副本)`,
        content: currentRecord.value.content,
        category: currentRecord.value.category,
        tags: [...currentRecord.value.tags],
        fields: { ...currentRecord.value.fields }
      })
      
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
    } catch (error) {
      uni.showToast({
        title: '复制失败',
        icon: 'error'
      })
    }
    closeActionMenu()
  }
}

const deleteRecord = () => {
  if (currentRecord.value) {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除这条记录吗？此操作不可恢复。',
      success: async (res) => {
        if (res.confirm && currentRecord.value) {
          try {
            await notebookStore.deleteRecord(currentRecord.value.id)
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          } catch (error) {
            uni.showToast({
              title: '删除失败',
              icon: 'error'
            })
          }
        }
      }
    })
    closeActionMenu()
  }
}

// 生命周期
onMounted(async () => {
  await notebookStore.initialize()
})
</script>

<style scoped>
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.fab {
  transition: all 0.3s ease;
}

.fab:active {
  transform: scale(0.95);
}

.record-card {
  transition: all 0.2s ease;
}

.record-card:active {
  transform: translateY(1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
