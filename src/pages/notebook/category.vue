<template>
  <view class="category-page bg-secondary-50 min-h-screen">
    <!-- 页面头部 -->
    <view class="page-header bg-white p-4 shadow-sm">
      <view class="header-content">
        <text class="page-title text-xl font-bold text-secondary-800">分类管理</text>
        <text class="page-subtitle text-sm text-secondary-500 mt-1">
          管理记录分类，查看统计信息
        </text>
      </view>
    </view>

    <!-- 统计概览 -->
    <view class="stats-overview p-4">
      <view class="stats-card bg-white rounded-lg p-4 shadow-sm">
        <view class="stats-header flex items-center justify-between mb-3">
          <text class="stats-title text-lg font-semibold text-secondary-800">分类统计</text>
          <text class="i-mdi:chart-pie text-primary-500 text-xl"></text>
        </view>
        <view class="stats-grid grid grid-cols-2 gap-4">
          <view class="stat-item text-center">
            <text class="stat-number text-2xl font-bold text-primary-600">{{ categories.length }}</text>
            <text class="stat-label text-sm text-secondary-500 block">总分类</text>
          </view>
          <view class="stat-item text-center">
            <text class="stat-number text-2xl font-bold text-success-600">{{ recordStats.total }}</text>
            <text class="stat-label text-sm text-secondary-500 block">总记录</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据管理 -->
    <view class="data-management p-4">
      <view class="management-card bg-white rounded-lg p-4 shadow-sm">
        <view class="management-header flex items-center justify-between mb-3">
          <text class="management-title text-lg font-semibold text-secondary-800">数据管理</text>
          <text class="i-mdi:database text-primary-500 text-xl"></text>
        </view>
        <view class="management-actions flex gap-2">
          <button
            class="export-btn flex-1 bg-success-500 text-white px-3 py-2 rounded-lg text-sm flex items-center justify-center"
            @tap="exportData"
          >
            <text class="i-mdi:download mr-1"></text>
            导出数据
          </button>
          <button
            class="import-btn flex-1 bg-warning-500 text-white px-3 py-2 rounded-lg text-sm flex items-center justify-center"
            @tap="importData"
          >
            <text class="i-mdi:upload mr-1"></text>
            导入数据
          </button>
          <button
            class="clear-btn flex-1 bg-error-500 text-white px-3 py-2 rounded-lg text-sm flex items-center justify-center"
            @tap="clearAllData"
          >
            <text class="i-mdi:delete-sweep mr-1"></text>
            清空数据
          </button>
        </view>
      </view>
    </view>

    <!-- 分类列表 -->
    <view class="categories-section px-4 pb-20">
      <view class="section-header flex items-center justify-between mb-4">
        <text class="section-title text-lg font-semibold text-secondary-800">分类列表</text>
        <button
          class="add-category-btn bg-primary-500 text-white px-3 py-2 rounded-lg text-sm flex items-center"
          @tap="showAddCategoryDialog"
        >
          <text class="i-mdi:plus mr-1"></text>
          添加分类
        </button>
      </view>

      <view class="categories-list space-y-3">
        <view
          v-for="category in categories"
          :key="category.id"
          class="category-card bg-white rounded-lg p-4 shadow-sm border-l-4"
          :style="{ borderLeftColor: category.color }"
        >
          <view class="category-header flex items-center justify-between mb-2">
            <view class="category-info flex items-center flex-1">
              <view
                class="category-icon w-10 h-10 rounded-full flex items-center justify-center mr-3"
                :style="{ backgroundColor: category.color + '20' }"
              >
                <text :class="category.icon" :style="{ color: category.color }" class="text-lg"></text>
              </view>
              <view class="category-details flex-1">
                <text class="category-name text-lg font-semibold text-secondary-800">
                  {{ category.name }}
                </text>
                <text v-if="category.description" class="category-description text-sm text-secondary-500 block">
                  {{ category.description }}
                </text>
              </view>
            </view>
            <view class="category-actions flex items-center">
              <view class="record-count bg-secondary-100 px-2 py-1 rounded text-sm text-secondary-600 mr-2">
                {{ recordStats.byCategory[category.id] || 0 }} 条
              </view>
              <text
                class="i-mdi:dots-vertical text-secondary-400 text-lg"
                @tap="showCategoryActions(category)"
              ></text>
            </view>
          </view>

          <!-- 最近记录预览 -->
          <view v-if="getRecentRecords(category.id).length > 0" class="recent-records mt-3">
            <text class="recent-title text-sm font-medium text-secondary-600 mb-2 block">最近记录</text>
            <view class="recent-list space-y-1">
              <view
                v-for="record in getRecentRecords(category.id).slice(0, 2)"
                :key="record.id"
                class="recent-item flex items-center p-2 bg-secondary-50 rounded"
                @tap="viewRecord(record.id)"
              >
                <text class="recent-title text-sm text-secondary-700 flex-1 line-clamp-1">
                  {{ record.title }}
                </text>
                <text class="recent-date text-xs text-secondary-500">
                  {{ formatDate(record.updatedAt) }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加分类弹窗 -->
    <uni-popup ref="addCategoryPopup" type="center">
      <view class="add-category-dialog bg-white rounded-lg p-6 mx-4">
        <view class="dialog-header mb-4">
          <text class="dialog-title text-lg font-semibold text-secondary-800">添加分类</text>
        </view>

        <view class="dialog-content space-y-4">
          <!-- 分类名称 -->
          <view class="form-group">
            <text class="form-label text-sm font-medium text-secondary-700 mb-2 block">
              分类名称 <text class="text-error-500">*</text>
            </text>
            <input
              v-model="newCategory.name"
              placeholder="请输入分类名称"
              class="form-input w-full p-3 border border-secondary-200 rounded-lg"
            />
          </view>

          <!-- 分类描述 -->
          <view class="form-group">
            <text class="form-label text-sm font-medium text-secondary-700 mb-2 block">分类描述</text>
            <textarea
              v-model="newCategory.description"
              placeholder="请输入分类描述（可选）"
              class="form-textarea w-full p-3 border border-secondary-200 rounded-lg min-h-20"
            />
          </view>

          <!-- 分类颜色 -->
          <view class="form-group">
            <text class="form-label text-sm font-medium text-secondary-700 mb-2 block">分类颜色</text>
            <view class="color-picker flex flex-wrap gap-2">
              <view
                v-for="color in colorOptions"
                :key="color"
                class="color-option w-8 h-8 rounded-full border-2 border-white shadow-sm"
                :class="{ 'ring-2 ring-primary-500': newCategory.color === color }"
                :style="{ backgroundColor: color }"
                @tap="selectColor(color)"
              ></view>
            </view>
          </view>

          <!-- 分类图标 -->
          <view class="form-group">
            <text class="form-label text-sm font-medium text-secondary-700 mb-2 block">分类图标</text>
            <view class="icon-picker flex flex-wrap gap-2">
              <view
                v-for="icon in iconOptions"
                :key="icon"
                class="icon-option w-10 h-10 rounded-lg border border-secondary-200 flex items-center justify-center"
                :class="{ 'bg-primary-100 border-primary-500': newCategory.icon === icon }"
                @tap="selectIcon(icon)"
              >
                <text :class="icon" class="text-lg text-secondary-600"></text>
              </view>
            </view>
          </view>
        </view>

        <view class="dialog-actions flex gap-3 mt-6">
          <button
            class="cancel-btn flex-1 bg-secondary-200 text-secondary-700 py-2 rounded-lg"
            @tap="closeAddCategoryDialog"
          >
            取消
          </button>
          <button
            class="confirm-btn flex-1 bg-primary-500 text-white py-2 rounded-lg"
            @tap="addCategory"
          >
            添加
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 分类操作菜单 -->
    <uni-popup ref="categoryActionPopup" type="bottom">
      <view class="category-action-menu bg-white rounded-t-lg p-4">
        <view class="menu-title text-lg font-semibold text-secondary-800 mb-4 text-center">
          分类操作
        </view>
        <view class="menu-actions space-y-2">
          <view
            class="action-item flex items-center p-3 rounded-lg bg-secondary-50"
            @tap="viewCategoryRecords"
          >
            <text class="i-mdi:eye text-primary-500 text-lg mr-3"></text>
            <text class="text-secondary-800">查看记录</text>
          </view>
          <view
            class="action-item flex items-center p-3 rounded-lg bg-secondary-50"
            @tap="editCategory"
          >
            <text class="i-mdi:pencil text-warning-500 text-lg mr-3"></text>
            <text class="text-secondary-800">编辑分类</text>
          </view>
          <view
            v-if="!isDefaultCategory(currentCategory?.id)"
            class="action-item flex items-center p-3 rounded-lg bg-secondary-50"
            @tap="deleteCategory"
          >
            <text class="i-mdi:delete text-error-500 text-lg mr-3"></text>
            <text class="text-secondary-800">删除分类</text>
          </view>
        </view>
        <view class="cancel-btn mt-4">
          <view
            class="cancel-action bg-secondary-200 text-center py-3 rounded-lg"
            @tap="closeCategoryActionMenu"
          >
            <text class="text-secondary-600">取消</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useNotebookStore } from '@/stores/notebook'
import type { Category, NoteRecord } from '@/types'

const notebookStore = useNotebookStore()

// 响应式数据
const addCategoryPopup = ref()
const categoryActionPopup = ref()
const currentCategory = ref<Category | null>(null)

// 新分类表单数据
const newCategory = reactive({
  name: '',
  description: '',
  color: '#14b8a6',
  icon: 'mdi:note-text'
})

// 颜色选项
const colorOptions = [
  '#14b8a6', '#3b82f6', '#8b5cf6', '#f59e0b',
  '#ef4444', '#10b981', '#f97316', '#6366f1',
  '#ec4899', '#84cc16', '#06b6d4', '#64748b'
]

// 图标选项
const iconOptions = [
  'mdi:note-text', 'mdi:wechat', 'mdi:alipay', 'mdi:credit-card',
  'mdi:api', 'mdi:database', 'mdi:folder', 'mdi:bookmark',
  'mdi:star', 'mdi:heart', 'mdi:lightbulb', 'mdi:cog'
]

// 计算属性
const { categories, records, recordStats } = storeToRefs(notebookStore)

// 方法
const getRecentRecords = (categoryId: string) => {
  return notebookStore.recordsByCategory(categoryId)
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

const showAddCategoryDialog = () => {
  // 重置表单
  newCategory.name = ''
  newCategory.description = ''
  newCategory.color = '#14b8a6'
  newCategory.icon = 'mdi:note-text'
  
  addCategoryPopup.value?.open()
}

const closeAddCategoryDialog = () => {
  addCategoryPopup.value?.close()
}

const selectColor = (color: string) => {
  newCategory.color = color
}

const selectIcon = (icon: string) => {
  newCategory.icon = icon
}

const addCategory = async () => {
  if (!newCategory.name.trim()) {
    uni.showToast({
      title: '请输入分类名称',
      icon: 'error'
    })
    return
  }

  try {
    await notebookStore.addCategory({
      name: newCategory.name.trim(),
      description: newCategory.description.trim(),
      color: newCategory.color,
      icon: newCategory.icon
    })

    uni.showToast({
      title: '添加成功',
      icon: 'success'
    })

    closeAddCategoryDialog()
  } catch (error) {
    console.error('添加分类失败:', error)
    uni.showToast({
      title: '添加失败',
      icon: 'error'
    })
  }
}

const showCategoryActions = (category: Category) => {
  currentCategory.value = category
  categoryActionPopup.value?.open()
}

const closeCategoryActionMenu = () => {
  categoryActionPopup.value?.close()
}

const viewCategoryRecords = () => {
  if (currentCategory.value) {
    uni.navigateTo({
      url: `/pages/notebook/index?category=${currentCategory.value.id}`
    })
  }
  closeCategoryActionMenu()
}

const editCategory = () => {
  // TODO: 实现编辑分类功能
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
  closeCategoryActionMenu()
}

const deleteCategory = () => {
  if (!currentCategory.value) return

  const recordCount = recordStats.value.byCategory[currentCategory.value.id] || 0
  
  if (recordCount > 0) {
    uni.showToast({
      title: '该分类下还有记录，无法删除',
      icon: 'error'
    })
    return
  }

  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个分类吗？此操作不可恢复。',
    success: async (res) => {
      if (res.confirm && currentCategory.value) {
        try {
          // TODO: 实现删除分类功能
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        } catch (error) {
          uni.showToast({
            title: '删除失败',
            icon: 'error'
          })
        }
      }
    }
  })
  
  closeCategoryActionMenu()
}

const isDefaultCategory = (categoryId?: string) => {
  // 默认分类不允许删除
  const defaultCategories = [
    'wechat-miniprogram',
    'alipay-miniprogram', 
    'payment-config',
    'api-config',
    'database-config',
    'general'
  ]
  return categoryId ? defaultCategories.includes(categoryId) : false
}

const viewRecord = (recordId: string) => {
  uni.navigateTo({
    url: `/pages/notebook/detail?id=${recordId}`
  })
}

// 数据管理功能
const exportData = () => {
  try {
    const exportData = notebookStore.exportData()
    const fileName = `notebook_backup_${new Date().toISOString().split('T')[0]}.json`

    // 在小程序中，我们将数据复制到剪贴板
    uni.setClipboardData({
      data: exportData,
      success: () => {
        uni.showModal({
          title: '导出成功',
          content: '数据已复制到剪贴板，您可以将其保存到文件中进行备份。',
          showCancel: false
        })
      },
      fail: () => {
        uni.showToast({
          title: '导出失败',
          icon: 'error'
        })
      }
    })
  } catch (error) {
    console.error('导出数据失败:', error)
    uni.showToast({
      title: '导出失败',
      icon: 'error'
    })
  }
}

const importData = () => {
  uni.showModal({
    title: '导入数据',
    content: '请先将备份数据复制到剪贴板，然后点击确定进行导入。',
    success: async (res) => {
      if (res.confirm) {
        try {
          const clipboardData = await uni.getClipboardData()
          if (clipboardData.data) {
            await notebookStore.importData(clipboardData.data)
            uni.showToast({
              title: '导入成功',
              icon: 'success'
            })
          } else {
            uni.showToast({
              title: '剪贴板为空',
              icon: 'error'
            })
          }
        } catch (error) {
          console.error('导入数据失败:', error)
          uni.showToast({
            title: '导入失败，请检查数据格式',
            icon: 'error'
          })
        }
      }
    }
  })
}

const clearAllData = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有数据吗？此操作不可恢复，建议先导出备份。',
    success: async (res) => {
      if (res.confirm) {
        try {
          await notebookStore.clearAllData()
          uni.showToast({
            title: '清空成功',
            icon: 'success'
          })
        } catch (error) {
          console.error('清空数据失败:', error)
          uni.showToast({
            title: '清空失败',
            icon: 'error'
          })
        }
      }
    }
  })
}

// 生命周期
onMounted(async () => {
  await notebookStore.initialize()
})
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.min-h-20 {
  min-height: 5rem;
}

.ring-2 {
  box-shadow: 0 0 0 2px currentColor;
}

.ring-primary-500 {
  --tw-ring-color: #14b8a6;
}

button {
  border: none;
  outline: none;
}

button:active {
  opacity: 0.8;
}

.color-option:active,
.icon-option:active {
  transform: scale(0.95);
}
</style>
