<template>
  <view class="edit-page bg-secondary-50 min-h-screen">
    <view class="form-container p-4 space-y-4">
      <!-- 基本信息 -->
      <view class="basic-info bg-white rounded-lg p-4 shadow-sm">
        <view class="section-header mb-4">
          <text class="section-title text-lg font-semibold text-secondary-800">基本信息</text>
        </view>

        <!-- 标题 -->
        <view class="form-group mb-4">
          <text class="form-label text-sm font-medium text-secondary-700 mb-2 block">
            标题 <text class="text-error-500">*</text>
          </text>
          <input
            v-model="formData.title"
            placeholder="请输入记录标题"
            class="form-input w-full p-3 border border-secondary-200 rounded-lg bg-white text-secondary-800"
            :class="{ 'border-error-500': errors.title }"
          />
          <text v-if="errors.title" class="error-text text-error-500 text-sm mt-1 block">
            {{ errors.title }}
          </text>
        </view>

        <!-- 分类选择 -->
        <view class="form-group mb-4">
          <text class="form-label text-sm font-medium text-secondary-700 mb-2 block">
            分类 <text class="text-error-500">*</text>
          </text>
          <view class="category-selector">
            <picker
              :value="categoryIndex"
              :range="categoryOptions"
              range-key="name"
              @change="onCategoryChange"
            >
              <view class="picker-display flex items-center justify-between p-3 border border-secondary-200 rounded-lg bg-white">
                <view class="selected-category flex items-center">
                  <text v-if="selectedCategory" :class="selectedCategory.icon" class="mr-2"></text>
                  <text class="text-secondary-800">
                    {{ selectedCategory?.name || '请选择分类' }}
                  </text>
                </view>
                <text class="i-mdi:chevron-down text-secondary-400"></text>
              </view>
            </picker>
          </view>
          <text v-if="errors.category" class="error-text text-error-500 text-sm mt-1 block">
            {{ errors.category }}
          </text>
        </view>

        <!-- 标签 -->
        <view class="form-group mb-4">
          <text class="form-label text-sm font-medium text-secondary-700 mb-2 block">标签</text>
          <view class="tags-input">
            <view class="tags-display flex flex-wrap gap-2 mb-2">
              <view
                v-for="(tag, index) in formData.tags"
                :key="index"
                class="tag-item flex items-center bg-primary-100 text-primary-700 px-2 py-1 rounded text-sm"
              >
                <text class="tag-text">#{{ tag }}</text>
                <text
                  class="i-mdi:close ml-1 text-primary-500"
                  @tap="removeTag(index)"
                ></text>
              </view>
            </view>
            <view class="tag-input-row flex gap-2">
              <input
                v-model="newTag"
                placeholder="添加标签"
                class="flex-1 p-2 border border-secondary-200 rounded bg-white text-secondary-800"
                @confirm="addTag"
              />
              <button
                class="add-tag-btn bg-primary-500 text-white px-3 py-2 rounded"
                @tap="addTag"
              >
                添加
              </button>
            </view>
          </view>
        </view>

        <!-- 内容 -->
        <view class="form-group">
          <text class="form-label text-sm font-medium text-secondary-700 mb-2 block">
            内容 <text class="text-error-500">*</text>
          </text>
          <textarea
            v-model="formData.content"
            placeholder="请输入记录内容"
            class="form-textarea w-full p-3 border border-secondary-200 rounded-lg bg-white text-secondary-800 min-h-32"
            :class="{ 'border-error-500': errors.content }"
            auto-height
          />
          <text v-if="errors.content" class="error-text text-error-500 text-sm mt-1 block">
            {{ errors.content }}
          </text>
        </view>
      </view>

      <!-- 自定义字段 -->
      <view v-if="customFields.length > 0" class="custom-fields bg-white rounded-lg p-4 shadow-sm">
        <view class="section-header mb-4">
          <text class="section-title text-lg font-semibold text-secondary-800">详细信息</text>
          <text class="section-subtitle text-sm text-secondary-500 mt-1">
            根据所选分类自动显示相关字段
          </text>
        </view>

        <view class="fields-list space-y-4">
          <view
            v-for="field in customFields"
            :key="field.key"
            class="field-group"
          >
            <text class="field-label text-sm font-medium text-secondary-700 mb-2 block">
              {{ field.label }}
              <text v-if="field.required" class="text-error-500">*</text>
            </text>

            <!-- 文本输入 -->
            <input
              v-if="field.type === 'text' || field.type === 'email' || field.type === 'url'"
              v-model="formData.fields[field.key]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :type="field.type"
              class="field-input w-full p-3 border border-secondary-200 rounded-lg bg-white text-secondary-800"
              :class="{ 'border-error-500': errors[`field_${field.key}`] }"
            />

            <!-- 密码输入 -->
            <view v-else-if="field.type === 'password'" class="password-input relative">
              <input
                v-model="formData.fields[field.key]"
                :placeholder="field.placeholder || `请输入${field.label}`"
                :password="!showPassword[field.key]"
                class="field-input w-full p-3 pr-12 border border-secondary-200 rounded-lg bg-white text-secondary-800"
                :class="{ 'border-error-500': errors[`field_${field.key}`] }"
              />
              <text
                :class="showPassword[field.key] ? 'i-mdi:eye-off' : 'i-mdi:eye'"
                class="password-toggle absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400"
                @tap="togglePasswordVisibility(field.key)"
              ></text>
            </view>

            <!-- 数字输入 -->
            <input
              v-else-if="field.type === 'number'"
              v-model="formData.fields[field.key]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              type="number"
              class="field-input w-full p-3 border border-secondary-200 rounded-lg bg-white text-secondary-800"
              :class="{ 'border-error-500': errors[`field_${field.key}`] }"
            />

            <!-- 多行文本 -->
            <textarea
              v-else-if="field.type === 'textarea'"
              v-model="formData.fields[field.key]"
              :placeholder="field.placeholder || `请输入${field.label}`"
              class="field-textarea w-full p-3 border border-secondary-200 rounded-lg bg-white text-secondary-800 min-h-24"
              :class="{ 'border-error-500': errors[`field_${field.key}`] }"
              auto-height
            />

            <text v-if="errors[`field_${field.key}`]" class="error-text text-error-500 text-sm mt-1 block">
              {{ errors[`field_${field.key}`] }}
            </text>

            <text v-if="field.description" class="field-description text-xs text-secondary-500 mt-1 block">
              {{ field.description }}
            </text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons bg-white rounded-lg p-4 shadow-sm">
        <view class="button-group flex gap-3">
          <button
            class="cancel-btn flex-1 bg-secondary-200 text-secondary-700 py-3 rounded-lg text-center"
            @tap="cancel"
          >
            取消
          </button>
          <button
            class="save-btn flex-1 bg-primary-500 text-white py-3 rounded-lg text-center"
            :class="{ 'opacity-50': saving }"
            :disabled="saving"
            @tap="save"
          >
            {{ saving ? '保存中...' : (isEdit ? '更新' : '保存') }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useNotebookStore } from '@/stores/notebook'
import type { NoteRecord, FieldTemplate } from '@/types'
import { CATEGORY_FIELD_TEMPLATES } from '@/types'

const notebookStore = useNotebookStore()

// 响应式数据
const recordId = ref('')
const isEdit = ref(false)
const saving = ref(false)
const newTag = ref('')
const showPassword = reactive<Record<string, boolean>>({})

// 表单数据
const formData = reactive({
  title: '',
  content: '',
  category: '',
  tags: [] as string[],
  fields: {} as Record<string, any>
})

// 表单验证错误
const errors = reactive<Record<string, string>>({})

// 计算属性
const { categories } = storeToRefs(notebookStore)

const categoryOptions = computed(() => categories.value)

const categoryIndex = computed(() => {
  return categories.value.findIndex(cat => cat.id === formData.category)
})

const selectedCategory = computed(() => {
  return categories.value.find(cat => cat.id === formData.category)
})

const customFields = computed((): FieldTemplate[] => {
  if (!formData.category) return []
  return CATEGORY_FIELD_TEMPLATES[formData.category] || []
})

// 监听分类变化，重置自定义字段
watch(() => formData.category, (newCategory, oldCategory) => {
  if (newCategory !== oldCategory) {
    formData.fields = {}
    // 初始化新分类的字段
    const fields = CATEGORY_FIELD_TEMPLATES[newCategory] || []
    fields.forEach(field => {
      formData.fields[field.key] = ''
    })
  }
})

// 方法
const onCategoryChange = (e: any) => {
  const index = e.detail.value
  if (index >= 0 && index < categories.value.length) {
    formData.category = categories.value[index].id
  }
}

const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !formData.tags.includes(tag)) {
    formData.tags.push(tag)
    newTag.value = ''
  }
}

const removeTag = (index: number) => {
  formData.tags.splice(index, 1)
}

const togglePasswordVisibility = (fieldKey: string) => {
  showPassword[fieldKey] = !showPassword[fieldKey]
}

const validateForm = (): boolean => {
  // 清空之前的错误
  Object.keys(errors).forEach(key => {
    delete errors[key]
  })

  let isValid = true

  // 验证标题
  if (!formData.title.trim()) {
    errors.title = '请输入标题'
    isValid = false
  }

  // 验证分类
  if (!formData.category) {
    errors.category = '请选择分类'
    isValid = false
  }

  // 验证内容
  if (!formData.content.trim()) {
    errors.content = '请输入内容'
    isValid = false
  }

  // 验证自定义字段
  customFields.value.forEach(field => {
    if (field.required && !formData.fields[field.key]?.trim()) {
      errors[`field_${field.key}`] = `请输入${field.label}`
      isValid = false
    }
  })

  return isValid
}

const save = async () => {
  if (!validateForm()) {
    uni.showToast({
      title: '请检查表单信息',
      icon: 'error'
    })
    return
  }

  saving.value = true

  try {
    const recordData = {
      title: formData.title.trim(),
      content: formData.content.trim(),
      category: formData.category,
      tags: formData.tags,
      fields: { ...formData.fields }
    }

    if (isEdit.value && recordId.value) {
      await notebookStore.updateRecord(recordId.value, recordData)
      uni.showToast({
        title: '更新成功',
        icon: 'success'
      })
    } else {
      await notebookStore.addRecord(recordData)
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
    }

    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)

  } catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'error'
    })
  } finally {
    saving.value = false
  }
}

const cancel = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消编辑吗？未保存的内容将丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    }
  })
}

const loadRecord = async (id: string) => {
  const record = notebookStore.getRecord(id)
  if (record) {
    formData.title = record.title
    formData.content = record.content
    formData.category = record.category
    formData.tags = [...record.tags]
    formData.fields = { ...record.fields }
  }
}

// 生命周期
onMounted(async () => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || currentPage.$route?.query

  if (options?.id) {
    recordId.value = options.id as string
    isEdit.value = true
  }

  await notebookStore.initialize()

  if (isEdit.value && recordId.value) {
    await loadRecord(recordId.value)
  } else {
    // 新建记录时设置默认分类
    if (categories.value.length > 0) {
      formData.category = notebookStore.settings.defaultCategory || categories.value[0].id
    }
  }
})
</script>

<style scoped>
.form-input,
.form-textarea,
.field-input,
.field-textarea {
  border: 1px solid #e2e8f0;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.field-input:focus,
.field-textarea:focus {
  border-color: #14b8a6;
  outline: none;
}

.min-h-32 {
  min-height: 8rem;
}

.min-h-24 {
  min-height: 6rem;
}

button {
  border: none;
  outline: none;
}

button:active {
  opacity: 0.8;
}

button:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.password-toggle {
  cursor: pointer;
}
</style>
