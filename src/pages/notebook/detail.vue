<template>
  <view class="detail-page bg-secondary-50 min-h-screen">
    <view v-if="loading" class="loading-state text-center py-12">
      <text class="i-svg-spinners:ring-resize text-primary-500 text-2xl"></text>
      <text class="block text-secondary-500 mt-2">加载中...</text>
    </view>

    <view v-else-if="!record" class="error-state text-center py-12">
      <text class="i-mdi:alert-circle-outline text-error-500 text-6xl block"></text>
      <text class="text-secondary-500 mt-4 block">记录不存在</text>
      <view class="mt-6">
        <button
          class="bg-primary-500 text-white px-6 py-2 rounded-lg"
          @tap="goBack"
        >
          返回
        </button>
      </view>
    </view>

    <view v-else class="record-detail">
      <!-- 记录头部 -->
      <view class="record-header bg-white p-4 shadow-sm">
        <view class="header-content">
          <view class="title-section mb-3">
            <text class="record-title text-xl font-bold text-secondary-800 leading-relaxed">
              {{ record.title }}
            </text>
          </view>

          <view class="meta-section">
            <view class="meta-row flex items-center mb-2">
              <text :class="categoryIcon" class="text-lg mr-2"></text>
              <text class="category-name text-secondary-600 mr-4">{{ categoryName }}</text>
              <view
                class="category-badge px-2 py-1 rounded text-xs text-white"
                :style="{ backgroundColor: categoryColor }"
              >
                {{ categoryName }}
              </view>
            </view>

            <view class="meta-row flex items-center text-sm text-secondary-500 mb-2">
              <text class="i-mdi:calendar-clock mr-2"></text>
              <text class="mr-4">创建：{{ formatDateTime(record.createdAt) }}</text>
            </view>

            <view class="meta-row flex items-center text-sm text-secondary-500">
              <text class="i-mdi:update mr-2"></text>
              <text>更新：{{ formatDateTime(record.updatedAt) }}</text>
            </view>
          </view>

          <!-- 标签 -->
          <view v-if="record.tags.length > 0" class="tags-section mt-3">
            <view class="tags-list flex flex-wrap gap-2">
              <text
                v-for="tag in record.tags"
                :key="tag"
                class="tag px-2 py-1 bg-primary-100 text-primary-700 text-sm rounded"
              >
                #{{ tag }}
              </text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons flex gap-3 mt-4">
          <button
            class="flex-1 bg-primary-500 text-white py-2 px-4 rounded-lg flex items-center justify-center"
            @tap="editRecord"
          >
            <text class="i-mdi:pencil mr-2"></text>
            编辑
          </button>
          <button
            class="flex-1 bg-secondary-200 text-secondary-700 py-2 px-4 rounded-lg flex items-center justify-center"
            @tap="shareRecord"
          >
            <text class="i-mdi:share-variant mr-2"></text>
            分享
          </button>
          <button
            class="bg-error-500 text-white py-2 px-4 rounded-lg flex items-center justify-center"
            @tap="deleteRecord"
          >
            <text class="i-mdi:delete"></text>
          </button>
        </view>
      </view>

      <!-- 记录内容 -->
      <view class="record-content bg-white mt-2 p-4 shadow-sm">
        <view class="content-header mb-3">
          <text class="section-title text-lg font-semibold text-secondary-800">内容</text>
        </view>
        <view class="content-body">
          <text class="content-text text-secondary-700 leading-relaxed whitespace-pre-wrap">
            {{ record.content }}
          </text>
        </view>
      </view>

      <!-- 自定义字段 -->
      <view v-if="hasCustomFields" class="custom-fields bg-white mt-2 p-4 shadow-sm">
        <view class="fields-header mb-3">
          <text class="section-title text-lg font-semibold text-secondary-800">详细信息</text>
        </view>
        <view class="fields-list space-y-3">
          <view
            v-for="(value, key) in record.fields"
            :key="key"
            class="field-item"
          >
            <view class="field-label text-sm font-medium text-secondary-600 mb-1">
              {{ getFieldLabel(key) }}
            </view>
            <view class="field-value bg-secondary-50 p-3 rounded-lg">
              <text
                v-if="isPasswordField(key)"
                class="field-text text-secondary-800 font-mono"
              >
                {{ showPasswords ? value : '••••••••••••' }}
              </text>
              <text v-else class="field-text text-secondary-800 break-all">
                {{ value }}
              </text>
              <view v-if="value" class="field-actions mt-2 flex gap-2">
                <button
                  class="copy-btn bg-primary-100 text-primary-600 px-2 py-1 rounded text-xs"
                  @tap="copyToClipboard(value)"
                >
                  复制
                </button>
                <button
                  v-if="isPasswordField(key)"
                  class="toggle-btn bg-secondary-200 text-secondary-600 px-2 py-1 rounded text-xs"
                  @tap="togglePasswordVisibility"
                >
                  {{ showPasswords ? '隐藏' : '显示' }}
                </button>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作历史 -->
      <view class="operation-history bg-white mt-2 p-4 shadow-sm">
        <view class="history-header mb-3">
          <text class="section-title text-lg font-semibold text-secondary-800">操作历史</text>
        </view>
        <view class="history-list space-y-2">
          <view class="history-item flex items-center p-2 bg-secondary-50 rounded">
            <text class="i-mdi:plus-circle text-success-500 mr-3"></text>
            <view class="history-content flex-1">
              <text class="history-action text-secondary-800 text-sm">创建记录</text>
              <text class="history-time text-secondary-500 text-xs block">
                {{ formatDateTime(record.createdAt) }}
              </text>
            </view>
          </view>
          <view
            v-if="record.updatedAt !== record.createdAt"
            class="history-item flex items-center p-2 bg-secondary-50 rounded"
          >
            <text class="i-mdi:pencil-circle text-warning-500 mr-3"></text>
            <view class="history-content flex-1">
              <text class="history-action text-secondary-800 text-sm">最后更新</text>
              <text class="history-time text-secondary-500 text-xs block">
                {{ formatDateTime(record.updatedAt) }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useNotebookStore } from '@/stores/notebook'
import type { NoteRecord } from '@/types'
import { CATEGORY_FIELD_TEMPLATES } from '@/types'

const notebookStore = useNotebookStore()

// 响应式数据
const recordId = ref('')
const loading = ref(true)
const showPasswords = ref(false)

// 计算属性
const record = computed(() => {
  return recordId.value ? notebookStore.getRecord(recordId.value) : null
})

const categoryInfo = computed(() => {
  return record.value ? notebookStore.getCategoryById(record.value.category) : null
})

const categoryName = computed(() => categoryInfo.value?.name || '未知分类')
const categoryColor = computed(() => categoryInfo.value?.color || '#64748b')
const categoryIcon = computed(() => categoryInfo.value?.icon || 'i-mdi:note-text')

const hasCustomFields = computed(() => {
  return record.value && Object.keys(record.value.fields).length > 0
})

// 方法
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getFieldLabel = (key: string) => {
  if (!record.value) return key
  
  const templates = CATEGORY_FIELD_TEMPLATES[record.value.category]
  if (templates) {
    const template = templates.find(t => t.key === key)
    if (template) return template.label
  }
  
  // 默认标签映射
  const labelMap: Record<string, string> = {
    appId: 'APPID',
    appSecret: 'AppSecret',
    merchantId: '商户号',
    secretKey: '秘钥ID',
    publicKeyId: '公钥ID',
    certSerialNumber: '证书序列号',
    platformCertSerialNumber: '平台证书序列号'
  }
  
  return labelMap[key] || key
}

const isPasswordField = (key: string) => {
  const passwordFields = ['appSecret', 'secretKey', 'privateKey', 'password']
  return passwordFields.includes(key)
}

const togglePasswordVisibility = () => {
  showPasswords.value = !showPasswords.value
}

const copyToClipboard = (text: string) => {
  uni.setClipboardData({
    data: text,
    success: () => {
      uni.showToast({
        title: '已复制到剪贴板',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'error'
      })
    }
  })
}

const editRecord = () => {
  if (record.value) {
    uni.navigateTo({
      url: `/pages/notebook/edit?id=${record.value.id}`
    })
  }
}

const shareRecord = () => {
  if (!record.value) return
  
  const shareText = `${record.value.title}\n\n${record.value.content}`
  
  uni.share({
    provider: 'weixin',
    type: 0,
    title: record.value.title,
    summary: record.value.content,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: () => {
      // 如果分享失败，复制到剪贴板
      copyToClipboard(shareText)
    }
  })
}

const deleteRecord = () => {
  if (!record.value) return
  
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条记录吗？此操作不可恢复。',
    success: async (res) => {
      if (res.confirm && record.value) {
        try {
          await notebookStore.deleteRecord(record.value.id)
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
          goBack()
        } catch (error) {
          uni.showToast({
            title: '删除失败',
            icon: 'error'
          })
        }
      }
    }
  })
}

const goBack = () => {
  uni.navigateBack()
}

// 生命周期
onMounted(async () => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || currentPage.$route?.query
  
  if (options?.id) {
    recordId.value = options.id as string
  }
  
  await notebookStore.initialize()
  loading.value = false
})
</script>

<style scoped>
.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.break-all {
  word-break: break-all;
}

.leading-relaxed {
  line-height: 1.625;
}

button {
  border: none;
  outline: none;
}

button:active {
  opacity: 0.8;
}
</style>
