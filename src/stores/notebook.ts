import { defineStore } from 'pinia'
import type { NoteRecord, Category, SearchFilters, AppSettings } from '@/types'
import { DEFAULT_CATEGORIES } from '@/types'

interface NotebookState {
  records: NoteRecord[]
  categories: Category[]
  settings: AppSettings
  loading: boolean
  error: string | null
}

export const useNotebookStore = defineStore('notebook', {
  state: (): NotebookState => ({
    records: [],
    categories: [...DEFAULT_CATEGORIES],
    settings: {
      theme: 'light',
      defaultCategory: 'general',
      autoSave: true,
      exportFormat: 'json'
    },
    loading: false,
    error: null
  }),

  getters: {
    // 获取所有记录
    allRecords: (state) => state.records,

    // 根据分类获取记录
    recordsByCategory: (state) => (categoryId: string) => {
      return state.records.filter(record => record.category === categoryId)
    },

    // 搜索记录
    searchRecords: (state) => (filters: SearchFilters) => {
      let filtered = [...state.records]

      if (filters.keyword) {
        const keyword = filters.keyword.toLowerCase()
        filtered = filtered.filter(record =>
          record.title.toLowerCase().includes(keyword) ||
          record.content.toLowerCase().includes(keyword) ||
          record.tags.some(tag => tag.toLowerCase().includes(keyword))
        )
      }

      if (filters.category) {
        filtered = filtered.filter(record => record.category === filters.category)
      }

      if (filters.tags && filters.tags.length > 0) {
        filtered = filtered.filter(record =>
          filters.tags!.some(tag => record.tags.includes(tag))
        )
      }

      if (filters.dateRange) {
        const { start, end } = filters.dateRange
        filtered = filtered.filter(record => {
          const recordDate = new Date(record.createdAt)
          return recordDate >= new Date(start) && recordDate <= new Date(end)
        })
      }

      return filtered.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    },

    // 获取分类信息
    getCategoryById: (state) => (id: string) => {
      return state.categories.find(cat => cat.id === id)
    },

    // 获取记录统计
    recordStats: (state) => {
      const stats = {
        total: state.records.length,
        byCategory: {} as Record<string, number>
      }

      state.categories.forEach(cat => {
        stats.byCategory[cat.id] = state.records.filter(r => r.category === cat.id).length
      })

      return stats
    },

    // 获取所有标签
    allTags: (state) => {
      const tags = new Set<string>()
      state.records.forEach(record => {
        record.tags.forEach(tag => tags.add(tag))
      })
      return Array.from(tags).sort()
    }
  },

  actions: {
    // 初始化数据
    async initialize() {
      this.loading = true
      try {
        await this.loadFromStorage()
      } catch (error) {
        this.error = '初始化失败'
        console.error('初始化失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 从本地存储加载数据
    async loadFromStorage() {
      try {
        const recordsData = uni.getStorageSync('notebook_records')
        const categoriesData = uni.getStorageSync('notebook_categories')
        const settingsData = uni.getStorageSync('notebook_settings')

        if (recordsData) {
          this.records = JSON.parse(recordsData)
        } else {
          // 如果没有数据，添加示例记录
          await this.addSampleData()
        }

        if (categoriesData) {
          this.categories = JSON.parse(categoriesData)
        }

        if (settingsData) {
          this.settings = { ...this.settings, ...JSON.parse(settingsData) }
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        throw error
      }
    },

    // 添加示例数据
    async addSampleData() {
      const now = new Date().toISOString()

      const sampleRecord: NoteRecord = {
        id: this.generateId(),
        title: '绿茵堂花茶小程序配置',
        content: '这是一个示例记录，展示了如何存储微信小程序的配置信息。您可以编辑或删除这条记录，然后添加您自己的记录。',
        category: 'wechat-miniprogram',
        tags: ['微信小程序', '配置', '示例'],
        fields: {
          appId: 'wxaabcd194c3ead52d',
          appSecret: '53925b1674b3a3e1cfc3d2701e861bb2',
          merchantId: '1721303998',
          secretKey: 'svnptpxx78wufuskxzof40fdbcpid4ur',
          publicKeyId: 'PUB_KEY_ID_0117213039982025070200181651003202',
          certSerialNumber: '4523FAFAD8EE084642754675E465799C639E657E',
          platformCertSerialNumber: '47D9A4E01C4E2C7173317FF4B70C03F0DB84C064'
        },
        createdAt: now,
        updatedAt: now
      }

      this.records.push(sampleRecord)
    },

    // 保存到本地存储
    async saveToStorage() {
      try {
        uni.setStorageSync('notebook_records', JSON.stringify(this.records))
        uni.setStorageSync('notebook_categories', JSON.stringify(this.categories))
        uni.setStorageSync('notebook_settings', JSON.stringify(this.settings))
      } catch (error) {
        console.error('保存数据失败:', error)
        throw error
      }
    },

    // 添加记录
    async addRecord(record: Omit<NoteRecord, 'id' | 'createdAt' | 'updatedAt'>) {
      const now = new Date().toISOString()
      const newRecord: NoteRecord = {
        ...record,
        id: this.generateId(),
        createdAt: now,
        updatedAt: now
      }

      this.records.unshift(newRecord)
      
      if (this.settings.autoSave) {
        await this.saveToStorage()
      }

      return newRecord
    },

    // 更新记录
    async updateRecord(id: string, updates: Partial<NoteRecord>) {
      const index = this.records.findIndex(r => r.id === id)
      if (index === -1) {
        throw new Error('记录不存在')
      }

      this.records[index] = {
        ...this.records[index],
        ...updates,
        updatedAt: new Date().toISOString()
      }

      if (this.settings.autoSave) {
        await this.saveToStorage()
      }

      return this.records[index]
    },

    // 删除记录
    async deleteRecord(id: string) {
      const index = this.records.findIndex(r => r.id === id)
      if (index === -1) {
        throw new Error('记录不存在')
      }

      this.records.splice(index, 1)

      if (this.settings.autoSave) {
        await this.saveToStorage()
      }
    },

    // 获取单个记录
    getRecord(id: string) {
      return this.records.find(r => r.id === id)
    },

    // 添加分类
    async addCategory(category: Omit<Category, 'id'>) {
      const newCategory: Category = {
        ...category,
        id: this.generateId()
      }

      this.categories.push(newCategory)
      await this.saveToStorage()

      return newCategory
    },

    // 更新设置
    async updateSettings(settings: Partial<AppSettings>) {
      this.settings = { ...this.settings, ...settings }
      await this.saveToStorage()
    },

    // 导出数据
    exportData() {
      const data = {
        records: this.records,
        categories: this.categories,
        settings: this.settings,
        exportTime: new Date().toISOString()
      }

      return JSON.stringify(data, null, 2)
    },

    // 导入数据
    async importData(jsonData: string) {
      try {
        const data = JSON.parse(jsonData)
        
        if (data.records) {
          this.records = data.records
        }
        
        if (data.categories) {
          this.categories = data.categories
        }
        
        if (data.settings) {
          this.settings = { ...this.settings, ...data.settings }
        }

        await this.saveToStorage()
      } catch (error) {
        console.error('导入数据失败:', error)
        throw new Error('导入数据格式错误')
      }
    },

    // 清空所有数据
    async clearAllData() {
      this.records = []
      this.categories = [...DEFAULT_CATEGORIES]
      this.settings = {
        theme: 'light',
        defaultCategory: 'general',
        autoSave: true,
        exportFormat: 'json'
      }
      await this.saveToStorage()
    },

    // 生成唯一ID
    generateId() {
      return Date.now().toString(36) + Math.random().toString(36).substr(2)
    },

    // 清除错误
    clearError() {
      this.error = null
    }
  }
})
