<template>
  <view class="loading-spinner flex flex-col items-center justify-center py-8">
    <view class="spinner-container relative">
      <text class="i-svg-spinners:ring-resize text-primary-500 text-3xl"></text>
    </view>
    <text v-if="text" class="loading-text text-secondary-500 mt-3 text-sm">
      {{ text }}
    </text>
  </view>
</template>

<script setup lang="ts">
interface Props {
  text?: string
}

withDefaults(defineProps<Props>(), {
  text: '加载中...'
})
</script>

<style scoped>
.spinner-container {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
