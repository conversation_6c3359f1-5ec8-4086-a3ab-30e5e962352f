<template>
  <view class="empty-state flex flex-col items-center justify-center py-12 px-6">
    <view class="empty-icon mb-4">
      <text :class="icon" class="text-secondary-300 text-6xl"></text>
    </view>
    <text class="empty-title text-lg font-semibold text-secondary-600 mb-2 text-center">
      {{ title }}
    </text>
    <text v-if="description" class="empty-description text-sm text-secondary-500 text-center leading-relaxed mb-6">
      {{ description }}
    </text>
    <slot name="action">
      <button
        v-if="actionText && actionHandler"
        class="action-btn bg-primary-500 text-white px-6 py-3 rounded-lg flex items-center"
        @tap="actionHandler"
      >
        <text v-if="actionIcon" :class="actionIcon" class="mr-2"></text>
        {{ actionText }}
      </button>
    </slot>
  </view>
</template>

<script setup lang="ts">
interface Props {
  icon?: string
  title: string
  description?: string
  actionText?: string
  actionIcon?: string
  actionHandler?: () => void
}

withDefaults(defineProps<Props>(), {
  icon: 'i-mdi:inbox-outline'
})
</script>

<style scoped>
.leading-relaxed {
  line-height: 1.625;
}

button {
  border: none;
  outline: none;
}

button:active {
  opacity: 0.8;
  transform: scale(0.98);
}
</style>
