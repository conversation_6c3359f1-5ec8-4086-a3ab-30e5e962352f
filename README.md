# 我的记事本 - 智能记录管理应用

一个基于 uni-app + Vue 3 + TypeScript + Tailwind CSS 的现代化记事本应用，专为管理各种配置信息和记录而设计。

> 基于 [`weapp-tailwindcss`](https://github.com/sonofmagic/weapp-tailwindcss) 模板开发

## ✨ 特性

- 📝 **智能分类管理** - 预设微信小程序、支付配置等专业分类
- 🔍 **强大搜索功能** - 支持标题、内容、标签多维度搜索
- 🏷️ **标签系统** - 灵活的标签管理，快速定位记录
- 🎨 **现代化UI** - 基于 Tailwind CSS 的美观界面设计
- 💾 **本地存储** - 所有数据安全存储在本地
- 📤 **数据导入导出** - 支持数据备份和迁移
- 🔒 **隐私保护** - 敏感信息支持隐藏显示
- 📱 **跨平台支持** - 支持微信小程序、H5、APP等多平台

### 技术特性

- ⚡️ [Vue 3](https://github.com/vuejs/core) + [Vite](https://github.com/vitejs/vite) - 快速开发体验
- 🎨 [TailwindCSS](https://tailwindcss.com/) - 现代化原子化CSS框架
- 😃 [Iconify 图标](https://github.com/egoist/tailwindcss-icons) - 丰富的图标库
- 🍍 [Pinia](https://pinia.vuejs.org/) - 现代化状态管理
- 🦾 [TypeScript](https://www.typescriptlang.org/) - 类型安全保障

## 🚀 快速开始

### 环境要求

- Node.js 16+
- pnpm (推荐) 或 npm

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
# 微信小程序
pnpm dev

# H5
pnpm dev:h5

# APP
pnpm dev:app
```

### 构建发布

```bash
# 微信小程序
pnpm build

# H5
pnpm build:h5

# APP
pnpm build:app
```

## 📱 功能介绍

### 主要功能

1. **记录管理**
   - 创建、编辑、删除记录
   - 支持富文本内容
   - 自定义字段配置

2. **分类系统**
   - 预设专业分类（微信小程序、支付配置等）
   - 自定义分类创建
   - 分类统计和管理

3. **搜索筛选**
   - 关键词搜索
   - 分类筛选
   - 标签筛选
   - 日期范围筛选

4. **数据管理**
   - 数据导出备份
   - 数据导入恢复
   - 数据清空重置

### 预设分类

- **微信小程序** - 存储 APPID、AppSecret、商户号等配置
- **支付宝小程序** - 支付宝相关配置信息
- **支付配置** - 支付接口相关配置
- **API配置** - 接口地址和密钥配置
- **数据库配置** - 数据库连接信息
- **通用记录** - 其他类型的记录

## 🎯 使用场景

### 开发者配置管理
- 微信小程序 APPID、AppSecret
- 支付商户号、证书序列号
- API 接口地址和密钥
- 数据库连接配置

### 项目信息记录
- 项目相关的重要信息
- 第三方服务配置
- 部署相关配置
- 测试账号信息

### 个人知识管理
- 学习笔记整理
- 重要信息备忘
- 工作经验总结
- 技术方案记录

## 📝 示例数据

应用首次启动时会自动创建示例记录，展示如何存储微信小程序配置信息：

```
标题: 绿茵堂花茶小程序配置
分类: 微信小程序
字段:
- APPID: wxaabcd194c3ead52d
- AppSecret: 53925b1674b3a3e1cfc3d2701e861bb2
- 商户号: 1721303998
- 秘钥ID: svnptpxx78wufuskxzof40fdbcpid4ur
- 公钥ID: PUB_KEY_ID_0117213039982025070200181651003202
- 证书序列号: 4523FAFAD8EE084642754675E465799C639E657E
- 平台证书序列号: 47D9A4E01C4E2C7173317FF4B70C03F0DB84C064
```

## 🛠️ 技术栈

- **框架**: uni-app + Vue 3
- **语言**: TypeScript
- **样式**: Tailwind CSS + UnoCSS
- **状态管理**: Pinia
- **构建工具**: Vite
- **代码规范**: ESLint + Prettier

## 📁 项目结构

```
src/
├── components/          # 通用组件
│   ├── LoadingSpinner.vue
│   └── EmptyState.vue
├── pages/              # 页面
│   └── notebook/       # 记事本相关页面
│       ├── index.vue   # 主页面
│       ├── detail.vue  # 详情页面
│       ├── edit.vue    # 编辑页面
│       └── category.vue # 分类管理
├── stores/             # 状态管理
│   └── notebook.ts     # 记事本数据管理
├── types/              # 类型定义
│   └── index.ts        # 通用类型
└── static/             # 静态资源
    └── icons/          # 图标文件
```

## 🎨 主题色彩

应用采用现代化的蓝绿色调主题：

- **主色调**: `#14b8a6` (Teal-500)
- **辅助色**: 灰色系列
- **成功色**: `#22c55e` (Green-500)
- **警告色**: `#f59e0b` (Amber-500)
- **错误色**: `#ef4444` (Red-500)

## 🔧 自定义配置

### 添加新的分类模板

在 `src/types/index.ts` 中添加新的字段模板：

```typescript
export const YOUR_CATEGORY_FIELDS = [
  { key: 'field1', label: '字段1', type: 'text', required: true },
  { key: 'field2', label: '字段2', type: 'password', required: false }
]

// 添加到模板映射中
export const CATEGORY_FIELD_TEMPLATES: Record<string, FieldTemplate[]> = {
  'your-category': YOUR_CATEGORY_FIELDS,
  // ... 其他模板
}
```

### 修改主题色彩

在 `tailwind.config.ts` 中修改颜色配置：

```typescript
colors: {
  primary: {
    // 修改主色调
    500: '#your-color',
    // ... 其他色阶
  }
}
```

### 开发环境配置

使用 `vscode` 的开发者，请先安装 [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss) 智能提示插件

### 更换 Appid

把 `src/manifest.json` 中的 `appid`, 更换为你自己的 `appid`

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 相关链接

- [uni-app 官网](https://uniapp.dcloud.io/)
- [Vue 3 官网](https://v3.vuejs.org/)
- [Vite 官网](https://vitejs.dev/)
- [TailwindCSS 官网](https://tailwindcss.com/)
- [weapp-tailwindcss 官网](https://weapp-tw.icebreaker.top/)

---

**享受高效的记录管理体验！** 🎉
